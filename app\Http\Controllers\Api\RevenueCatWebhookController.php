<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\RevenueCatWebhookEvent;
use App\Models\User;
use App\Services\RevenueCatWebhookService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class RevenueCatWebhookController extends Controller
{
    public function __construct(
        private RevenueCatWebhookService $webhookService
    ) {}

    /**
     * Handle RevenueCat webhook events
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function handle(Request $request): JsonResponse
    {
        try {
            // Verify webhook signature
            if (!$this->webhookService->verifyWebhookSignature($request)) {
                Log::warning('RevenueCat webhook signature verification failed', [
                    'headers' => $request->headers->all(),
                    'ip' => $request->ip(),
                ]);
                
                return response()->json([
                    'error' => 'Invalid signature'
                ], 400);
            }

            $payload = $request->all();
            
            // Log the webhook event for debugging
            Log::info('RevenueCat webhook received', [
                'event_type' => $payload['event']['type'] ?? 'unknown',
                'customer_id' => $payload['event']['app_user_id'] ?? null,
                'environment' => $payload['event']['environment'] ?? null,
            ]);

            // Create webhook event record
            $webhookEvent = $this->createWebhookEvent($payload);

            // Process the webhook event
            $result = $this->webhookService->processWebhookEvent($webhookEvent);

            if ($result['success']) {
                $webhookEvent->markAsProcessed($result['subscription_id'] ?? null);
                
                return response()->json([
                    'success' => true,
                    'message' => 'Webhook processed successfully'
                ], 200);
            } else {
                $webhookEvent->markAsFailed($result['error'] ?? 'Unknown error');
                
                Log::error('RevenueCat webhook processing failed', [
                    'webhook_event_id' => $webhookEvent->id,
                    'error' => $result['error'] ?? 'Unknown error',
                    'payload' => $payload,
                ]);
                
                return response()->json([
                    'success' => false,
                    'error' => $result['error'] ?? 'Processing failed'
                ], 400);
            }

        } catch (\Exception $e) {
            Log::error('RevenueCat webhook exception', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'payload' => $request->all(),
            ]);

            return response()->json([
                'error' => 'Internal server error'
            ], 500);
        }
    }

    /**
     * Create a webhook event record
     *
     * @param array $payload
     * @return RevenueCatWebhookEvent
     */
    private function createWebhookEvent(array $payload): RevenueCatWebhookEvent
    {
        $event = $payload['event'] ?? [];
        
        // Try to find the user by RevenueCat app_user_id
        $user = null;
        if (!empty($event['app_user_id'])) {
            // Assuming app_user_id maps to user ID or email
            // You may need to adjust this based on your RevenueCat configuration
            //todo: replace 132 with $event['app_user_id']
            $user = User::where('id', 132)
                       ->first();
        }

        return RevenueCatWebhookEvent::create([
            'event_type' => $event['type'] ?? 'unknown',
            'revenuecat_customer_id' => $event['customer_info']['customer_id'] ?? null,
            'revenuecat_original_app_user_id' => $event['app_user_id'] ?? null,
            'product_id' => $event['product_id'] ?? null,
            'entitlement_id' => $event['entitlement_id'] ?? null,
            'store' => $event['store'] ?? null,
            'environment' => $event['environment'] ?? null,
            'webhook_payload' => $payload,
            'user_id' => $user?->id,
        ]);
    }
}
