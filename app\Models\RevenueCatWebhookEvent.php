<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class RevenueCatWebhookEvent extends Model
{
    use HasFactory;

    protected $fillable = [
        'event_type',
        'revenuecat_customer_id',
        'revenuecat_original_app_user_id',
        'product_id',
        'entitlement_id',
        'store',
        'environment',
        'webhook_payload',
        'processing_status',
        'processing_error',
        'processed_at',
        'user_id',
        'subscription_id',
    ];

    protected $casts = [
        'webhook_payload' => 'array',
        'processed_at' => 'datetime',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function subscription(): BelongsTo
    {
        return $this->belongsTo(Subscription::class);
    }

    /**
     * Mark the event as processed
     */
    public function markAsProcessed(?int $subscriptionId = null): void
    {
        $this->update([
            'processing_status' => 'processed',
            'processed_at' => now(),
            'subscription_id' => $subscriptionId,
        ]);
    }

    /**
     * Mark the event as failed
     */
    public function markAsFailed(string $error): void
    {
        $this->update([
            'processing_status' => 'failed',
            'processing_error' => $error,
            'processed_at' => now(),
        ]);
    }

    /**
     * Scope for pending events
     */
    public function scopePending($query)
    {
        return $query->where('processing_status', 'pending');
    }

    /**
     * Scope for processed events
     */
    public function scopeProcessed($query)
    {
        return $query->where('processing_status', 'processed');
    }

    /**
     * Scope for failed events
     */
    public function scopeFailed($query)
    {
        return $query->where('processing_status', 'failed');
    }
}
