<?php

namespace App\Services;

use App\Enums\SubscriptionPlan;
use App\Enums\SubscriptionStatus;
use App\Models\RevenueCatWebhookEvent;
use App\Models\Subscription;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class RevenueCatWebhookService
{
    /**
     * Verify webhook signature from RevenueCat
     *
     * @param Request $request
     * @return bool
     */
    public function verifyWebhookSignature(Request $request): bool
    {
        // $secret = config('services.revenuecat.webhook_secret');
        // if (empty($secret)) {
        //     Log::warning('RevenueCat webhook secret not configured');
        //     return false;
        // }

        // $authHeader = $request->header('Authorization');
        // if ($authHeader !== 'Bearer ' . $secret) { // Ensure the Authorization header is valid
        //     Log::warning('Invalid RevenueCat webhook Authorization header', ['header' => $authHeader]);
        //     return false;
        // }

        return true;
    }

    /**
     * Process a webhook event
     *
     * @param RevenueCatWebhookEvent $webhookEvent
     * @return array
     */
    public function processWebhookEvent(RevenueCatWebhookEvent $webhookEvent): array
    {
        try {
            $payload = $webhookEvent->webhook_payload;
            $event = $payload['event'] ?? [];
            $eventType = $event['type'] ?? '';

            // Find or create user
            $user = $this->findOrCreateUser($event);
            if (!$user) {
                return [
                    'success' => false,
                    'error' => 'Could not find or create user'
                ];
            }

            // Update webhook event with user
            $webhookEvent->update(['user_id' => $user->id]);

            // Process based on event type
            return match ($eventType) {
                'INITIAL_PURCHASE' => $this->handleInitialPurchase($user, $event),
                'RENEWAL' => $this->handleRenewal($user, $event),
                'CANCELLATION' => $this->handleCancellation($user, $event),
                'EXPIRATION' => $this->handleExpiration($user, $event),
                'BILLING_ISSUE' => $this->handleBillingIssue($user, $event),
                'PRODUCT_CHANGE' => $this->handleProductChange($user, $event),
                default => [
                    'success' => false,
                    'error' => "Unsupported event type: {$eventType}"
                ]
            };

        } catch (\Exception $e) {
            Log::error('Error processing RevenueCat webhook event', [
                'webhook_event_id' => $webhookEvent->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Find or create user from RevenueCat event
     *
     * @param array $event
     * @return User|null
     */
    private function findOrCreateUser(array $event): ?User
    {
        $appUserId = $event['app_user_id'] ?? null;
        
        if (empty($appUserId)) {
            return null;
        }

        // Try to find user by ID first, then by email
        $user = User::where('id', $appUserId)
                   ->orWhere('email', $appUserId)
                   ->first();

        // If user not found and app_user_id looks like an email, create anonymous user
        if (!$user && filter_var($appUserId, FILTER_VALIDATE_EMAIL)) {
            $user = User::create([
                'email' => $appUserId,
                'name' => 'RevenueCat User',
                'is_anonymous' => false,
                'user_type' => 'user',
                'email_verified_at' => now(),
            ]);
        }

        return $user;
    }

    /**
     * Handle initial purchase event
     *
     * @param User $user
     * @param array $event
     * @return array
     */
    private function handleInitialPurchase(User $user, array $event): array
    {
        return DB::transaction(function () use ($user, $event) {
            $subscription = $this->createOrUpdateSubscription($user, $event, SubscriptionStatus::ACTIVE);
            
            Log::info('RevenueCat initial purchase processed', [
                'user_id' => $user->id,
                'subscription_id' => $subscription->id,
                'product_id' => $event['product_id'] ?? null,
            ]);

            return [
                'success' => true,
                'subscription_id' => $subscription->id
            ];
        });
    }

    /**
     * Handle renewal event
     *
     * @param User $user
     * @param array $event
     * @return array
     */
    private function handleRenewal(User $user, array $event): array
    {
        return DB::transaction(function () use ($user, $event) {
            $subscription = $this->createOrUpdateSubscription($user, $event, SubscriptionStatus::ACTIVE);
            
            Log::info('RevenueCat renewal processed', [
                'user_id' => $user->id,
                'subscription_id' => $subscription->id,
                'product_id' => $event['product_id'] ?? null,
            ]);

            return [
                'success' => true,
                'subscription_id' => $subscription->id
            ];
        });
    }

    /**
     * Handle cancellation event
     *
     * @param User $user
     * @param array $event
     * @return array
     */
    private function handleCancellation(User $user, array $event): array
    {
        return DB::transaction(function () use ($user, $event) {
            $subscription = $this->findExistingSubscription($user, $event);
            
            if ($subscription) {
                $subscription->update([
                    'status' => SubscriptionStatus::CANCELED,
                    'cancellation_date' => now(),
                    'auto_renewal' => false,
                ]);
                
                Log::info('RevenueCat cancellation processed', [
                    'user_id' => $user->id,
                    'subscription_id' => $subscription->id,
                ]);

                return [
                    'success' => true,
                    'subscription_id' => $subscription->id
                ];
            }

            return [
                'success' => false,
                'error' => 'Subscription not found for cancellation'
            ];
        });
    }

    /**
     * Handle expiration event
     *
     * @param User $user
     * @param array $event
     * @return array
     */
    private function handleExpiration(User $user, array $event): array
    {
        return DB::transaction(function () use ($user, $event) {
            $subscription = $this->findExistingSubscription($user, $event);

            if ($subscription) {
                $subscription->update([
                    'status' => SubscriptionStatus::EXPIRED,
                    'auto_renewal' => false,
                ]);

                Log::info('RevenueCat expiration processed', [
                    'user_id' => $user->id,
                    'subscription_id' => $subscription->id,
                ]);

                return [
                    'success' => true,
                    'subscription_id' => $subscription->id
                ];
            }

            return [
                'success' => false,
                'error' => 'Subscription not found for expiration'
            ];
        });
    }

    /**
     * Handle billing issue event
     *
     * @param User $user
     * @param array $event
     * @return array
     */
    private function handleBillingIssue(User $user, array $event): array
    {
        return DB::transaction(function () use ($user, $event) {
            $subscription = $this->findExistingSubscription($user, $event);

            if ($subscription) {
                // Keep subscription active but log the billing issue
                // RevenueCat will handle retries and eventual expiration
                Log::warning('RevenueCat billing issue', [
                    'user_id' => $user->id,
                    'subscription_id' => $subscription->id,
                    'product_id' => $event['product_id'] ?? null,
                ]);

                return [
                    'success' => true,
                    'subscription_id' => $subscription->id
                ];
            }

            return [
                'success' => false,
                'error' => 'Subscription not found for billing issue'
            ];
        });
    }

    /**
     * Handle product change event
     *
     * @param User $user
     * @param array $event
     * @return array
     */
    private function handleProductChange(User $user, array $event): array
    {
        return DB::transaction(function () use ($user, $event) {
            $subscription = $this->createOrUpdateSubscription($user, $event, SubscriptionStatus::ACTIVE);

            Log::info('RevenueCat product change processed', [
                'user_id' => $user->id,
                'subscription_id' => $subscription->id,
                'new_product_id' => $event['product_id'] ?? null,
            ]);

            return [
                'success' => true,
                'subscription_id' => $subscription->id
            ];
        });
    }

    /**
     * Create or update subscription from RevenueCat event
     *
     * @param User $user
     * @param array $event
     * @param SubscriptionStatus $status
     * @return Subscription
     */
    private function createOrUpdateSubscription(User $user, array $event, SubscriptionStatus $status): Subscription
    {
        $customerInfo = $event['customer_info'] ?? [];
        $productId = $event['product_id'] ?? null;
        $entitlementId = $event['entitlement_id'] ?? null;

        // Map RevenueCat product ID to our subscription plan
        $plan = $this->mapProductIdToPlan($productId);

        // Try to find existing subscription by RevenueCat customer ID
        $subscription = Subscription::where('user_id', $user->id)
            ->where('revenuecat_customer_id', $customerInfo['customer_id'] ?? null)
            ->first();

        $subscriptionData = [
            'user_id' => $user->id,
            'plan' => $plan,
            'status' => $status,
            'start_date' => now(),
            'end_date' => isset($event['expiration_at_ms'])
                ? \Carbon\Carbon::createFromTimestampMs($event['expiration_at_ms'])
                : now()->addMonth(),
            'purchase_date' => isset($event['purchased_at_ms'])
                ? \Carbon\Carbon::createFromTimestampMs($event['purchased_at_ms'])
                : now(),
            'auto_renewal' => !($event['is_trial_period'] ?? false),
            'points' => $this->getPointsForPlan($plan),

            // RevenueCat specific fields
            'revenuecat_customer_id' => $customerInfo['customer_id'] ?? null,
            'revenuecat_original_app_user_id' => $event['app_user_id'] ?? null,
            'revenuecat_entitlement_id' => $entitlementId,
            'revenuecat_product_identifier' => $productId,
            'revenuecat_purchased_at' => isset($event['purchased_at_ms'])
                ? \Carbon\Carbon::createFromTimestampMs($event['purchased_at_ms'])
                : now(),
            'revenuecat_expires_at' => isset($event['expiration_at_ms'])
                ? \Carbon\Carbon::createFromTimestampMs($event['expiration_at_ms'])
                : now()->addMonth(),
            'revenuecat_store' => $event['store'] ?? null,
            'revenuecat_environment' => $event['environment'] ?? null,
            'revenuecat_is_trial_period' => $event['is_trial_period'] ?? false,
            'revenuecat_webhook_data' => $event,
        ];

        if ($subscription) {
            $subscription->update($subscriptionData);
        } else {
            $subscription = Subscription::create($subscriptionData);
        }

        return $subscription;
    }

    /**
     * Find existing subscription for user and event
     *
     * @param User $user
     * @param array $event
     * @return Subscription|null
     */
    private function findExistingSubscription(User $user, array $event): ?Subscription
    {
        $customerInfo = $event['customer_info'] ?? [];
        $customerId = $customerInfo['customer_id'] ?? null;

        if ($customerId) {
            return Subscription::where('user_id', $user->id)
                ->where('revenuecat_customer_id', $customerId)
                ->latest()
                ->first();
        }

        // Fallback to finding by product ID
        $productId = $event['product_id'] ?? null;
        if ($productId) {
            return Subscription::where('user_id', $user->id)
                ->where('revenuecat_product_identifier', $productId)
                ->latest()
                ->first();
        }

        return null;
    }

    /**
     * Map RevenueCat product ID to our subscription plan
     *
     * @param string|null $productId
     * @return SubscriptionPlan
     */
    private function mapProductIdToPlan(?string $productId): SubscriptionPlan
    {
        // Map your RevenueCat product IDs to subscription plans
        // Adjust these mappings based on your actual product IDs
        return match ($productId) {
            'rydo_plus_monthly', 'rydo_plus_yearly', 'com.rydo.plus.monthly', 'com.rydo.plus.yearly' => SubscriptionPlan::RYDO_PLUS,
            default => SubscriptionPlan::RYDO_PLUS, // Default to RYDO_PLUS
        };
    }

    /**
     * Get points for a subscription plan
     *
     * @param SubscriptionPlan $plan
     * @return int
     */
    private function getPointsForPlan(SubscriptionPlan $plan): int
    {
        return match ($plan) {
            SubscriptionPlan::RYDO_PLUS => 1000,
            SubscriptionPlan::BASIC => 500,
            default => 1000,
        };
    }
}
